function index07() {
    // 打印日志，标识函数执行，用于调试流程追踪
    console.log("index07");

    // 使用 axios 发起 GET 请求，获取品牌销售数据
    axios({
        method: 'GET',              // 请求方法为 GET
        url: 'http://localhost:8080/getplace'  // 数据接口地址
    }).then(function(res) {
        // 打印 API 响应数据，用于调试数据结构
        console.log('main7 API响应:', res.data);

        // 检查响应数据是否存在且为数组类型
        if (res.data && Array.isArray(res.data)) {
            // 将API数据映射为图表需要的数据格式（提取品牌名称和销售数量）
            var chartData = res.data.map(function(item) {
                return {
                    name: item.name,    // 品牌名称
                    value: item.sales   // 销售数量
                };
            });

            // 初始化并渲染气泡图
            initChart7(chartData);
        } else {
            // 数据格式不符合预期时打印错误信息
            console.error('main7 数据格式错误', res.data);
        }
    }).catch(function(error) {
        // 处理API请求失败的情况（如网络错误、接口报错）
        console.error('main7 API请求失败:', error);
    });
}

function initChart7(data) {
    // 初始化 ECharts 实例，使用 canvas 渲染器（提升性能）
    var myChart = echarts.init(document.getElementById('main7'), null, {
        renderer: 'canvas',
        useDirtyRect: false
    });

    // 处理数据：取前10个品牌数据，并添加size属性（限制气泡最大尺寸）
    const chartdata = data.slice(0, 10).map(item => ({
        name: item.name,
        value: item.value,
        size: Math.min(item.value, 1500)  // 气泡尺寸与销量关联，最大1500
    }));

    // 定义图表使用的颜色数组（气泡渐变色）
    const color = [
        "#ffaaff", "#55ffff", "#5d8b8b", "#aaff7f", "#aaff00", "#55aaff",
        "#aaaaff", "#ffaa7f", "#aa557f", "#454567", "#ffff7f", "#FFA500",
        "#8A2BE2", "#2E8B57", "#4169E1", "#DDA0DD", "#FF4500", "#4B0082",
        "#00FF00", "#1E90FF", "#FFDAB9", "#00FFFF", "#FF00FF", "#800080",
        "#008000", "#0000FF", "#FFD700", "#808080"
    ];

    // 定义隶书字体名称（根据实际系统调整，常见为"LiSu"）
    const liShuFont = "LiSu";

    // 构建系列数据，配置气泡样式、标签和提示信息
    const seriesData = chartdata.map((item, index) => ({
        // 气泡显示的名称（仅保留品牌名，去除销售数量）
        name: item.name,
        value: item.value,                // 销售数值（用于图表数据）
        symbolSize: item.size / 15,       // 气泡大小（销量/15）
        draggable: true,                  // 允许气泡拖拽交互
        label: {
            normal: {
                textStyle: {
                    fontSize: 18,           // 标签字体大小
                    color: "#ffffff",        // 标签字体颜色
                    fontFamily: liShuFont    // 标签字体设为隶书
                },
                // 标签显示品牌名称
                formatter: params => params.name
            }
        },
        itemStyle: {
            normal: {
                // 径向渐变填充（中心透明白到边缘彩色）
                color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [{
                    offset: 0,
                    color: "rgba(255,255,255,0.8)"
                }, {
                    offset: 1,
                    color: color[index % color.length]
                }]),
                opacity: 0.8,               // 气泡透明度
                borderWidth: 3,             // 气泡边框宽度
                borderColor: "#fff",        // 气泡边框颜色
                shadowBlur: 3,              // 阴影模糊半径
                shadowColor: "rgba(0,0,0,0.3)", // 阴影颜色
                symbol: 'circle'            // 气泡形状为圆形
            },
            // 鼠标悬停时的气泡样式（增强视觉反馈）
            emphasis: {
                opacity: 1,                 // 悬停时透明度为1（完全不透明）
                borderWidth: 5,             // 悬停时边框宽度增加
                shadowBlur: 10,             // 悬停时阴影增强
                shadowColor: "rgba(0,0,0,0.5)"
            }
        },
        // 气泡专属提示框配置（仅显示品牌和一个销售数量）
        tooltip: {
            show: true,                   // 显示提示框
            formatter: function(params) {
                // 自定义提示框内容，仅保留品牌和销售数量（去除重复信息）
                return `
                    <div style="background-color:rgba(0,0,0,0.8); padding:12px; border-radius:8px; color:#fff; font-family:${liShuFont};">
                        <div style="font-size:20px; font-weight:bold;">品牌：${params.name}</div>
                        <div style="font-size:18px; margin-top:5px;">销售数量：${params.value.toLocaleString()}</div>
                    </div>
                `;
            },
            backgroundColor: 'transparent', // 透明背景（使用HTML自定义样式）
            borderWidth: 0,                 // 无边框
            padding: 0,                     // 无内边距
            enterable: true,                // 允许鼠标进入提示框区域（避免移开即消失）
            textStyle: {
                fontFamily: liShuFont        // 提示框文本字体设为隶书
            }
        }
    }));

    // 配置 ECharts 图表选项
    const option = {
        animationDurationUpdate: 1000,    // 数据更新动画时长（1秒）
        animationEasing: "elasticOut",    // 动画缓动效果（弹性退出）
        cursor: "move",                   // 鼠标指针样式（移动图标）
        grid: {
            left: '0%',                   // 图表左侧边距
            right: '0%',                  // 图表右侧边距
            top: '7%',                    // 图表顶部边距
            bottom: '7%',                 // 图表底部边距
            containLabel: true,           // 确保标签在网格区域内
            textStyle: {
                fontFamily: liShuFont      // 网格文本字体设为隶书
            }
        },
        // 全局提示框配置（统一样式，气泡专属提示会覆盖）
        tooltip: {
            trigger: "item",              // 鼠标悬停在气泡上时触发
            triggerOn: "mousemove",       // 鼠标移动时更新提示
            transitionDuration: 0.3,      // 提示框过渡动画时长
            backgroundColor: "transparent",
            borderWidth: 0,
            padding: 0,
            axisPointer: {
                show: false                // 不显示坐标轴指示器
            },
            textStyle: {
                fontFamily: liShuFont      // 全局提示框字体设为隶书
            }
        },
        series: [{
            type: "graph",                // 使用图形类型（支持力导向布局）
            layout: "force",              // 力导向布局（气泡自动排列避免重叠）
            roam: true,                   // 允许鼠标拖动和缩放图表
            focusNodeAdjacency: true,     // 点击节点时高亮显示相关气泡
            itemStyle: {
                emphasis: {
                    scale: 1.2,             // 悬停时气泡放大1.2倍
                    shadowBlur: 25          // 悬停时阴影增强
                }
            },
            force: {
                repulsion: 300,            // 气泡之间的排斥力（值越大越分散）
                edgeLength: 90,            // 虚拟边长度（影响布局密度）
                layoutAnimation: true      // 启用布局动画
            },
            data: seriesData,             // 气泡数据
            edges: [],                    // 无边数据（独立气泡，无连接线）
            label: {
                normal: {
                    show: true,             // 显示气泡标签
                    position: 'top',        // 标签位于气泡上方
                    formatter: params => params.name, // 标签仅显示品牌名
                    textStyle: {
                        fontFamily: liShuFont  ,// 标签字体设为隶书
						fontSize:24,
                    }
                }
            }
        }]
    };

    // 应用配置选项到图表实例
    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }

    // 添加窗口大小变化监听器，确保图表自适应
    window.addEventListener('resize', myChart.resize);
}