function index06() {
    // 主函数：获取API数据并初始化图表
    console.log("index06");
    
    // 发送GET请求到后端API获取折扣数据
    axios({
        method: 'GET',
        url: 'http://localhost:8080/getdiscount'
    }).then(function(res) {
        // 打印API响应数据
        console.log('main6 API响应:', res.data);
        
        // 检查数据格式是否符合预期（数组类型）
        if (res.data && Array.isArray(res.data)) {
            // 将API返回的数据转换为图表所需格式
            var chartData = res.data.map(function(item) {
                return {
                    plan: item.price_drop,    // 降价金额
                    actual: item.discount,    // 折扣百分比
                    areaName: item.Models     // 车型类别
                };
            });
            
            // 使用转换后的数据初始化图表
            initChart6(chartData);
        } else {
            // 数据格式不符合预期时打印错误信息
            console.error('main6 数据格式错误', res.data);
        }
    }).catch(function(error) {
        // 请求失败时打印错误信息
        console.error('main6 API请求失败:', error);
    });
}

function initChart6(data){
    // 图表初始化函数：配置并渲染ECharts图表
    
    // 创建ECharts实例，绑定到页面中的DOM元素
    var myChart = echarts.init(document.getElementById('main6'));
    
    // 初始化数据数组
    var planCnt = []; // 存储价格数据
    var actualCnt = []; // 存储折扣数据
    var colnames = []; // 存储车型名称（X轴标签）
    

    // 遍历处理API数据，提取图表所需的各项数据
    $(data).each(function(index , element){
        planCnt.push(element.plan);
        actualCnt.push(element.actual);
        colnames.push(element.areaName);
    });

    // 定义隶书字体名称（根据实际系统调整，常见为"LiSu"）
    const liShuFont = "LiSu";

    // 配置图表选项
    option = {
        // 鼠标悬停提示框配置
        "tooltip": {
            "trigger": "axis",
            transitionDuration: 0,
            backgroundColor: 'rgba(83,93,105,0.8)',
            borderColor: '#535b69',
            borderRadius: 8,
            borderWidth: 2,
            padding: [5, 10],
            formatter: function (params) {
                // 自定义提示框内容格式
                var res = '';
                for (var i = 0, l = params.length; i < l; i++) {
                    res += '' + params[i].seriesName + ' : ' + params[i].value + '<br>';
                }
                return res;
            },
            axisPointer: {
                type: 'line',
                lineStyle: {
                    type: 'dashed',
                    color: '#ffffff'
                }
            },
            textStyle: {
                fontFamily: liShuFont  ,// 提示框文本字体设为隶书
				fontSize:24,
            }
        },
        
        // 图表网格区域配置
        "grid": {
            "top": '50',
            "left": '45',
            "right": '10',
            "bottom": '30',
            textStyle: {
                color: "#fff",
                fontFamily: liShuFont  // 网格文本字体设为隶书
            }
        },
        
        // 图例配置
        "legend": {
            right: '24',
            top: "24",
            itemWidth: 8,
            itemHeight: 12,
            textStyle: {
                color: '#fff',
                fontSize: 20,
                fontFamily: liShuFont  // 图例文本字体设为隶书
            },
            "data": ['价格','折扣'],
        },
        
        // 启用数据计算功能
        "calculable": true,
        
        // X轴配置
        xAxis: [{
            'type': 'category',
            "axisTick": {
                "show": false // 不显示X轴刻度线
            },
            "axisLine": {
                "show": true, // 显示X轴底线
                lineStyle: {
                    color: '#ffffff',
                    width: 2 // 设置底线宽度为2像素
                }
            },
            "axisLabel": {
                show: true,
                interval: 0,
                fontSize: 24,
                color: '#ffffff',
                margin: 8,
                fontFamily: liShuFont  // X轴标签字体设为隶书
            },
            "splitArea": {
                "show": false // 不显示X轴分隔区域
            },
            'data': colnames, // 设置X轴数据（车型名称）
            splitLine: {
                show: false // 不显示X轴网格线
            }
        }],
        
        // Y轴配置
        "yAxis": [
            {
                "type": "value",
                offset: -14,
                "splitLine": {
                    "show": false // 不显示Y轴网格线
                },
                "axisLine": {
                    "show": true, // 显示Y轴左侧线
                    lineStyle: {
                        color: '#ffffff',
                        width: 2 // 设置左侧线宽度为2像素
                    }
                },
                "axisTick": {
                    "show": false // 不显示Y轴刻度线
                },
                "axisLabel": {
                    "interval": 0,
                    fontSize: 20,
                    color: '#ffffff',
                    fontFamily: liShuFont  // Y轴标签字体设为隶书
                },
                "splitArea": {
                    "show": false // 不显示Y轴分隔区域
                }
            }
        ],

        // 图表系列配置（柱状图）
        "series": [
            {
                "name": "价格",
                "type": "bar",
                "barGap": "10%",
                itemStyle: {//图形样式
                    normal: {
                        "color": '#55aaff' // 设置价格柱状图颜色
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: "top",
                        textStyle: {
                            color: "#ffffff",
                            fontSize: 18,
                            fontFamily: liShuFont  // 价格柱状图标签字体设为隶书
                        }
                    }
                },
                data: planCnt, // 设置价格数据
                barWidth: 28, // 设置柱状图宽度
            },{
                name:'折扣',
                type:'bar',
                itemStyle : {  /*设置折线颜色*/
                    normal : {
                      color:'#ff5500' // 设置折扣柱状图颜色
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: "top",
                        textStyle: {
                            color: "#ffffff",
                            fontSize: 18,
                            fontFamily: liShuFont  // 折扣柱状图标签字体设为隶书
                        }
                    }
                },
                data:actualCnt, // 设置折扣数据
                barWidth: 28, // 设置柱状图宽度
            }
        ]
    };

    // 使用配置项和数据渲染图表
    myChart.setOption(option);
}