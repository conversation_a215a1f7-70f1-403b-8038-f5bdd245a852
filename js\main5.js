// 页面初始化函数：获取关注度数据并初始化图表
function index05() {
    console.log("index05"); // 打印日志标识函数执行
    
    // 发送GET请求获取关注度数据
    axios({
        method: 'GET',
        url: 'http://localhost:8080/getattention'
    }).then(function(res) {
        console.log('main5 API响应:', res.data); // 打印原始数据用于调试
        
        // 处理数据逻辑分支
        if (res.data && Array.isArray(res.data)) {
            // 初始化图表数据结构
            var chartData = {
                total: [],   // 当年每月总关注度
                last: []     // 往年每月总关注度
            };

            // 按月份分组计算总关注度
            var monthlyData = {};
            res.data.forEach(function(item) {
                const month = item.month;
                // 初始化月份数据（若不存在）并累加关注度
                if (!monthlyData[month]) monthlyData[month] = 0;
                monthlyData[month] += item.attention || 0;
            });

            // 填充12个月完整数据（含空值处理）
            for (let i = 1; i <= 12; i++) {
                const currentValue = monthlyData[i] || 0;
                chartData.total.push(currentValue);
                // 生成往年数据（当前值的80%-90%随机浮动）
                chartData.last.push(Math.floor(currentValue * (0.8 + Math.random() * 0.1)));
            }

            console.log('处理后的图表数据:', chartData);
            initChart5(chartData); // 调用图表初始化函数
        } 
        // 兼容API直接返回期望格式的情况
        else if (res.data && res.data.total && res.data.last) {
            initChart5(res.data); // 直接使用返回数据
        } 
        // 数据异常时使用模拟数据
        else {
            console.error('main5 数据格式错误，使用模拟数据', res.data);
            // 模拟数据（当年/往年关注度趋势）
            const mockData = {
                total: [1200, 1350, 1420, 1580, 1650, 1720, 1850, 1920, 2100, 2250, 2380, 2500],
                last: [1100, 1250, 1320, 1480, 1550, 1620, 1750, 1820, 2000, 2150, 2280, 2400]
            };
            initChart5(mockData); // 使用模拟数据初始化图表
        }
    }).catch(function(error) {
        console.error('main5 API请求失败:', error); // 错误处理
    });
}


/**
 * 初始化关注度对比图表
 * @param {Object} data - 包含当年和往年关注度数据的对象
 */
function initChart5(data) {
    console.log("初始化关注度图表，数据:", data);
    
    // 获取图表容器并清理旧实例
    const chartDom = document.getElementById('main5');
    if (!chartDom) {
        console.error('找不到main5容器');
        return;
    }
    
    // 设置固定尺寸（关键调整点）
    chartDom.style.width = '1000px';
    chartDom.style.height = '600px';
    
    echarts.dispose(chartDom);
    const myChart = echarts.init(chartDom);

    // 准备图表数据
    const XData = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"];
    const yData1 = data.total; // 当年数据
    const yData2 = data.last;  // 往年数据

    // 定义隶书字体名称（根据实际系统调整，常见为"LiSu"）
    const liShuFont = "LiSu";

    // 配置图表选项
    const option = {
        backgroundColor: "", // 透明背景
        
        // 优化图例配置
        legend: {
            type: 'plain',
            orient: 'horizontal',
            right: '1%',           // 调整右侧边距
            top: '1%',             // 调整顶部边距
            textStyle: {
                color: '#ffffff',
                fontSize: 18,
                fontFamily: liShuFont  // 图例字体设为隶书
            },
            itemWidth: 20,          // 增大图例图标宽度
            itemHeight: 14,         // 增大图例图标高度
            data: [
                {name: '当年关注度', icon: 'rect'},
                {name: '往年关注度', icon: 'line'}
            ]
        },
        
        // X轴配置（月份轴）
        xAxis: {
            axisTick: { show: false },
            splitLine: { show: false },
            splitArea: { show: false },
            data: XData,
            axisLabel: {
                formatter: function(value) {
                    const maxLength = 1;
                    const valLength = value.length;
                    if (valLength > maxLength) {
                        let ret = "";
                        for (let i = 0; i < Math.ceil(valLength / maxLength); i++) {
                            ret += value.substring(i * maxLength, (i + 1) * maxLength) + "\n";
                        }
                        return ret;
                    }
                    return value;
                },
                interval: 0,
                fontSize: 20,         // 增大X轴标签字体
                fontWeight: 100,
                textStyle: { 
                    color: '#ffffff',
                    fontFamily: liShuFont  // X轴标签字体设为隶书
                }
            },
            axisLine: { lineStyle: { color: '#ffffff' } }
        },
        
        // Y轴配置（关注度数值轴）
        yAxis: {
            axisTick: { show: false },
            splitLine: { show: false },
            splitArea: { show: false },
            axisLabel: {
                textStyle: { 
                    color: '#ffffff', 
                    fontSize: 20,
                    fontFamily: liShuFont  // Y轴标签字体设为隶书
                }
            },
            axisLine: { lineStyle: { color: '#ffffff' } }
        },
        
        // 调整图表边距，扩大显示区域
        grid: {
            borderWidth: 0,
            top: '15%',             // 减少顶部边距
            left: '4%',             // 减少左侧边距
            right: '4%',            // 减少右侧边距
            bottom: '0%',          // 减少底部边距
            containLabel: true,
            textStyle: { 
                color: "#fff",
                fontFamily: liShuFont  // 网格文本字体设为隶书
            }
        },
        
        // 提示框配置
        tooltip: {
            trigger: "axis",
            transitionDuration: 0,
            backgroundColor: 'rgba(60, 67, 76, 0.8)',
            borderColor: '#2a2f36',
            borderRadius: 8,
            borderWidth: 2,
            padding: [5, 10],
            formatter: function(params) {
                let res = '';
                params.forEach(item => {
                    res += item.seriesName + ' : ' + item.value + '<br>';
                });
                return res;
            },
            axisPointer: {
                type: 'line',
                lineStyle: { type: 'dashed', color: '#ffffff' }
            },
            textStyle: {
                fontFamily: liShuFont  ,// 提示框文本字体设为隶书
				fontSize:24,
            }
        },
        
        // 图表系列配置
        series: [
            {
                name: '当年关注度',
                type: "bar",
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: '#6cdaa2' },
                                { offset: 1, color: '#55aaff' }
                            ]
                        },
                        barBorderRadius: 15,
                    }
                },
                label: {
                    normal: {
                        show: true,
                        position: "top",
                        textStyle: { 
                            color: "#ffffff", 
                            fontSize: 16,
                            fontFamily: liShuFont  // 柱状图标签字体设为隶书
                        }
                    }
                },
                data: yData1,
                barWidth: 24,           // 增加柱状图宽度
            },
            {
                name: '往年关注度',
                type: 'line',
                data: yData2,
                itemStyle: {
                    normal: {
                        lineStyle: { width: 3 } // 增加折线宽度
                    }
                },
                label: {
                    normal: {
                        show: true,
                        textStyle: { 
                            color: "#ffffff",
							  fontSize: 16,
                            fontFamily: liShuFont  // 折线图标签字体设为隶书
                        }
                    }
                }
            }
        ]
    };

    myChart.setOption(option);
    console.log('关注度图表配置已应用');

    // 注册到响应式管理器（若存在）
    if (typeof ResponsiveChartManager !== 'undefined') {
        ResponsiveChartManager.register(myChart);
    }
    
    // 可选：添加窗口大小变化时的自适应
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}