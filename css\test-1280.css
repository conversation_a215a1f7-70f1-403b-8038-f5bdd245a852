@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@font-face {
	font-family: myFirstFont;
	src: url('../font/Fette-Engschrift.ttf');
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

/* 
body{
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
/* 	background-image: url('../images/11.jpg'; */
/* color: #ffffff;
    overflow-x: hidden;
} */


body {
	font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	background-image: url('../images/11.jpg');
	/* 插入图片，路径根据实际情况调整 */
	background-size: cover;
	/* 图片覆盖整个背景 */
	background-position: center;
	/* 图片居中 */
	background-repeat: no-repeat;
	/* 不重复图片 */
	background-attachment: fixed;
	/* 背景固定，滚动时不移动 */
	color: #ffffff;
	overflow-x: hidden;
}

/* 现代化背景动画 */
/* body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
} */

/* 现代化背景动画 */
body::before {
	content: '';
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: transparent;
	/* 透明背景，不遮挡图片 */
	z-index: -1;
	animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: 0.8;
	}
}

/* 现代化加载动画 */
.loading {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100px;
}

.loading::after {
	content: '';
	width: 40px;
	height: 40px;
	border: 3px solid rgba(102, 126, 234, 0.3);
	border-top: 3px solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* 现代化渐入动画 */
.fade-in {
	animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 现代化脉冲效果 */
.pulse {
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
	}

	70% {
		box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
	}

	100% {
		box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
	}
}

div {
	box-sizing: border-box;
}

.clearfix {
	clear: both;
}

/* 现代化工具提示 */
.tooltip {
	position: relative;
	display: inline-block;
}

.tooltip .tooltiptext {
	visibility: hidden;
	width: 120px;
	background: rgba(0, 0, 0, 0.8);
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 8px;
	position: absolute;
	z-index: 1;
	bottom: 125%;
	left: 50%;
	margin-left: -60px;
	opacity: 0;
	transition: opacity 0.3s;
	font-size: 12px;
}

.tooltip:hover .tooltiptext {
	visibility: visible;
	opacity: 1;
}

/* 现代化选择样式 */
::selection {
	background: rgba(102, 126, 234, 0.3);
	color: #ffffff;
}

::-moz-selection {
	background: rgba(102, 126, 234, 0.3);
	color: #ffffff;
}

/* 现代化头部样式 */
.container-header {
	width: 100%;
	height: 80px;
	text-align: center;
	background: rgba(255, 255, 255, 0.02);
	backdrop-filter: blur(20px);
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	position: relative;
	z-index: 10;
}

.container-header .nowTime {
	position: absolute;
	left: 30px;
	top: 50%;
	transform: translateY(-50%);
	display: flex;
	gap: 20px;
}

.container-header .nowTime li {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px 12px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	font-size: 18px;
	font-weight: 600;
	color: #ffffff;
	min-width: 100px;
}

.nowTime li div,
.nowTime li p {
	font-size: 11px;
	color: rgba(255, 255, 255, 0.7);
	margin-top: 2px;
	font-weight: 400;
}

.container-header .location {
	position: absolute;
	right: 30px;
	top: 50%;
	transform: translateY(-50%);
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 16px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 20px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.location i {
	font-size: 16px;
	color: #64b5f6;
}

.location span {
	font-size: 14px;
	font-weight: 500;
	color: #ffffff;
}

.container-header h3 {
	line-height: 80px;
	font-size: 56px;
	font-weight: 700;
	background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	letter-spacing: 1px;
}


/* 现代化装饰边角 */
.left-top,
.right-top,
.left-bottom,
.right-bottom {
	position: absolute;
	width: 20px;
	height: 20px;
	z-index: 2;
}

.left-top {
	left: 0;
	top: 0;
	border-left: 2px solid #356a4e;
	border-top: 2px solid #316248;
	border-radius: 4px 0 0 0;
}

.right-top {
	right: 0;
	top: 0;
	border-right: 2px solid #356a4e;
	border-top: 2px solid #316248;
	border-radius: 0 4px 0 0;
}

.left-bottom {
	left: 0;
	bottom: 0;
	border-left: 2px solid #356a4e;
	border-bottom: 2px solid #316248;
	border-radius: 0 0 0 4px;
}

.right-bottom {
	right: 0;
	bottom: 0;
	border-right: 2px solid#356a4e;
	border-bottom: 2px solid #316248;
	border-radius: 0 0 4px 0;
}

/* 添加发光效果 */
.left-top::before,
.right-top::before,
.left-bottom::before,
.right-bottom::before {
	content: '';
	position: absolute;
	width: 6px;
	height: 6px;
	background: #1d3b2b;
	border-radius: 50%;
	box-shadow: 0 0 10px #667eea;
}

.left-top::before {
	top: -3px;
	left: -3px;
}

.right-top::before {
	top: -3px;
	right: -3px;
}

.left-bottom::before {
	bottom: -3px;
	left: -3px;
}

.right-bottom::before {
	bottom: -3px;
	right: -3px;
}

/* 现代化标题样式 */
.com-count-title {
	color: #ffffff;
	padding: 20px 24px 16px;
	font-size: 35px;
	font-weight: 600;
	position: relative;
	background: linear-gradient(90deg, #ffffff 0%, #afafaf 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.com-count-title::after {
	content: '';
	position: absolute;
	bottom: 8px;
	left: 24px;
	width: 40px;
	height: 3px;
	background: linear-gradient(90deg, #ffffff 0%, #c2c2c2 100%);
	border-radius: 2px;
}

.com-screen-content,
.com-screen-content2 {
	width: 100%;
	height: auto;
	padding: 0 20px 50px;
	position: relative;
}

/* 图表容器现代化样式 */
#main1,
#main2,
#main3,
#main4,
#main5,
#main6,
#main7 {
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 255, 255, 0.05);
	transition: all 0.3s ease;
}

#main1:hover,
#main2:hover,
#main3:hover,
#main4:hover,
#main5:hover,
#main6:hover,
#main7:hover {
	background: rgba(255, 255, 255, 0.03);
	border-color: rgba(102, 126, 234, 0.2);
}

.filter-type {
	font-size: 0;
}

.filter-type li:hover {
	cursor: pointer;
}

.filter-type li {
	display: inline-block;
	width: 120px;
	line-height: 40px;
	font-family: myFirstFont;
	font-size: 20px;
	text-align: center;
	color: #024f9b;
	border: solid 1px #075797;
	background: #0d2343;
}

.filter-type li.active {
	color: white;
	background: #0c182d;
	border: solid 1px #1bb9f9;
}


/* 现代化容器布局 */
.container-content {
	padding: 20px 30px;
	box-sizing: border-box;
	display: grid;
	gap: 20px;
}

.count-base,
.count-resource,
.count-share,
.count-topic {
	position: relative;
	background: rgba(255, 255, 255, 0.03);
	backdrop-filter: blur(20px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 16px;
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
}

.count-base:hover,
.count-resource:hover,
.count-share:hover,
.count-topic:hover {
	transform: translateY(-2px);
	box-shadow:
		0 12px 40px rgba(85, 170, 127, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
	border-color: rgba(85, 170, 127, 0.3);
}

/* 网格布局 */
.top {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 20px;
	margin-bottom: 20px;
	align-items: stretch;
	/* 确保所有子元素高度一致 */
}

/* 第一排容器统一高度和特殊样式 */
.top .count-base,
.top .count-resource {
	height: 380px;
}

/* 第一排图表容器需要更多底部空间 */
.top .com-screen-content {
	padding: 0 20px 30px;
}

.mid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	margin-bottom: 20px;
}

.bottom {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
}

.count-base {
	height: 380px;
	width: 640px;
	
}

.count-resource {
	height: 380px;
	width: 580px;
	
}

.count-share {
	height: 380px;
	width: 910px;
	
}

.count-topic {
	height: 350px;
	width: 910px;
	
}

/* 现代化数据标签 */
.data-label {
	position: absolute;
	top: 20px;
	right: 20px;
	display: flex;
	gap: 8px;
}

.data-label li {
	padding: 8px 16px;
	text-align: center;
	font-size: 13px;
	font-weight: 500;
	color: rgba(255, 255, 255, 0.6);
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 80px;
}

.data-label li:hover,
.data-label li.active {
	color: #ffffff;
	background: linear-gradient(135deg, #ffffff 0%, #764ba2 100%);
	border-color: rgba(102, 126, 234, 0.5);
	transform: translateY(-1px);
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}



@charset "utf-8";
/* author lyc */

* {
	margin: 0px;
	padding: 0px;
	font-family: '微软雅黑';
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

li {
	list-style: none
}

img {
	border: none
}

a {
	text-decoration: none;
}

/* 现代化数据表格样式 */
.topRec_List {
	width: 100%;
	margin: 0 auto;
	background: rgba(255, 255, 255, 0.02);
	border-radius: 12px;
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.topRec_List dl {
	width: 100%;
	background: rgba(255, 255, 255, 0.05);
	margin: 0;
	padding: 0;
}

.topRec_List dd {
	float: left;
	text-align: center;
	border-bottom: 2px solid rgba(102, 126, 234, 0.3);
	color: #667eea;
	font-size: 13px;
	font-weight: 600;
	padding: 12px 8px;
	background: rgba(255, 255, 255, 0.03);
}

.topRec_List dl dd:nth-child(1) {
	width: 25%;
	text-align: left;
	padding-left: 16px;
}

.topRec_List dl dd:nth-child(2) {
	width: 15%;
}

.topRec_List dl dd:nth-child(3) {
	width: 15%;
}

.topRec_List dl dd:nth-child(4) {
	width: 15%;
}

.topRec_List dl dd:nth-child(5) {
	width: 15%;
}

.topRec_List dl dd:nth-child(6) {
	width: 15%;
}

.maquee {
	height: 240px;
	overflow: hidden;
}

.topRec_List ul {
	width: 100%;
	height: 240px;
	margin: 0;
	padding: 0;
}

.topRec_List li {
	width: 100%;
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 12px;
	color: rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.topRec_List li:hover {
	background: rgba(102, 126, 234, 0.1);
	color: #ffffff;
}

.topRec_List li:nth-child(2n) {
	background: rgba(255, 255, 255, 0.02);
}

.topRec_List li div {
	float: left;
	padding: 0 8px;
}

.topRec_List li div:nth-child(1) {
	width: 25%;
	text-align: left;
	padding-left: 16px;
	font-weight: 500;
}

.topRec_List li div:nth-child(2) {
	width: 15%;
}

.topRec_List li div:nth-child(3) {
	width: 15%;
}

.topRec_List li div:nth-child(4) {
	width: 15%;
}

.topRec_List li div:nth-child(5) {
	width: 15%;
}

.topRec_List li div:nth-child(6) {
	width: 15%;
}


/* 现代化数据卡片 */
.use-data {
	display: flex;
	justify-content: space-between;
	gap: 12px;
	margin: 16px 0;
	padding: 0;
}

.use-data li {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 12px;
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 12px;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.use-data li::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.use-data li:hover {
	transform: translateY(-2px);
	background: rgba(255, 255, 255, 0.08);
	border-color: rgba(102, 126, 234, 0.3);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.use-data .data-count {
	color: #ffffff;
	font-family: 'Inter', sans-serif;
	font-size: 24px;
	font-weight: 700;
	line-height: 1;
	margin-bottom: 4px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	animation: numberPulse 2s ease-in-out infinite;
}

@keyframes numberPulse {

	0%,
	100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.05);
	}
}

/* 可爱风格的数据卡片 */
.cute-sedan {
	background: linear-gradient(135deg, #ffffff 0%, #FF8E9B 100%) !important;
	-webkit-background-clip: text !important;
	-webkit-text-fill-color: transparent !important;
	background-clip: text !important;
	animation: cuteGlow 2s ease-in-out infinite alternate !important;
}

.cute-suv {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%) !important;
	-webkit-background-clip: text !important;
	-webkit-text-fill-color: transparent !important;
	background-clip: text !important;
	animation: cuteGlow 2s ease-in-out infinite alternate !important;
}

.cute-mpv {
	background: linear-gradient(135deg, #FFE66D 0%, #ffffff 100%) !important;
	-webkit-background-clip: text !important;
	-webkit-text-fill-color: transparent !important;
	background-clip: text !important;
	animation: cuteGlow 2s ease-in-out infinite alternate !important;
}

@keyframes cuteGlow {
	0% {
		filter: brightness(1) drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
		transform: scale(1);
	}

	100% {
		filter: brightness(1.2) drop-shadow(0 0 15px rgba(255, 255, 255, 0.6));
		transform: scale(1.02);
	}
}

/* 可爱的数据名称样式 */
.com-screen-content2 .use-data li .data-name {
	font-size: 11px !important;
	color: #ffffff !important;
	margin-top: 4px !important;
	letter-spacing: 0.5px !important;
}

.use-data .data-name {
	color: rgba(255, 255, 255, 0.7);
	font-size: 14px;
	font-weight: 500;
	line-height: 1.2;
	text-align: center;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb {
	background: linear-gradient(135deg, #ffffff 0%, #ffaaff 100%);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(135deg, #ffffff 0%, #667eea 100%);
}

/* 响应式设计 */
@media (max-width: 1440px) {
	.container-content {
		padding: 15px 20px;
	}

	.count-base {
		height: 360px;
	}

	.count-resource {
		height: 360px;
	}

	.count-share {
		height: 360px;
	}

	.count-topic {
		height: 320px;
	}
}

@media (max-width: 1200px) {

	.top,
	.mid,
	.bottom {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.count-base,
	.count-resource,
	.count-share,
	.count-topic {
		height: auto;
		min-height: 300px;
	}
}

* {
	margin: 0px;
	padding: 0px;
	/* font-family: "KaiTi", "STKaiti", "Microsoft YaHei", sans-serif; */
	font-family: "LiSu", "KaiTi", "STKaiti", "SimSun", sans-serif;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}