<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>新能源汽车销售数据分析</title>
		<link rel="stylesheet" href="icon/iconfont.css">
		<script src="js/jquery.min.js"></script>
		<script src="js/echarts.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
	</head>
	<body>
		<div class="container-header">
			<ul class="nowTime">
				<li></li>
				<li></li>
			</ul>
			<div class="location">
				<i class="icon iconfont icon-buoumaotubiao23"></i>
				<span class="areaName"></span>
			</div>
			<h3>新能源汽车销售数据分析</h3>
		</div>
		<div class="container-content">
			<div class="top">
				<!-- 上左 1-1 -->
				<div class="count-base">
					<div class="com-count-title">各个品牌的销量情况</div>
					<div class="com-screen-content">
						<div id="main1" style="width:100%;height:300px;"></div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
				<!-- 上中  1-2 -->
				<div class="count-resource q1">
					<div class="com-count-title">比亚迪——不同车型销售占比</div>
					<div class="com-screen-content2">
						<ul class="use-data">
						</ul>
						<div class="com-screen-content">
							<div id="main2" style="margin-top:5px;width:100%;height:200px;"></div>
						</div>
						<span class="left-top"></span>
						<span class="right-top"></span>
						<span class="left-bottom"></span>
						<span class="right-bottom"></span>
					</div>
				</div>
				<!-- 上右  1-3 -->
				<div class="count-resource q2">
					<div class="com-count-title">成都各个品牌的销售数量</div>
					<div class="com-screen-content">
						<div id="main7" style="width:100%;height:260px;"></div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
			</div>


			<div class="mid">
				<!-- 中左  2-1 -->
				<div class="count-share w1">

					<div class="com-count-title">比亚迪——不同车型销售价格及折扣</div>
					<div class="com-screen-content">
						<div id="main6" style="width:100%;height:260px;"></div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
				
				<!-- 中右  2-2 -->
				<div class="count-share w2">
					<div class="com-count-title">比亚迪——每月关注度</div>
					<div class="com-screen-content">
						<div id="main5" style="width:100%;height:282px;"></div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
			</div>

			<div class="bottom">
				<!-- 下左 3-1 -->
				<div class="count-topic e1">

					<div class="com-count-title">比亚迪——每月不同车型销售数量</div>
					<div class="com-screen-content">
						<div class="topRec_List">
							<dl>
								<dd style="font-size: 20px; color: #ffffff;">品牌</dd>
								<dd style="font-size: 20px; color: #ffffff;">月份</dd>
								<dd style="font-size: 20px; color: #ffffff;">轿车销量</dd>
								<dd style="font-size: 20px; color: #ffffff;">SUV销量</dd>
								<dd style="font-size: 20px; color: #ffffff;">MPV销量</dd>
								<dd style="font-size: 20px; color: #ffffff;">总销量</dd>
							</dl>
							<div class="maquee" id="main4">
								<ul>
								</ul>
							</div>
						</div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
				<!-- 下右 3-2 -->
				<div class="count-topic e2">
				
					<div class="com-count-title">纯电与混动每月的销售数量</div>
					<div class="com-screen-content">
						<ul class="data-label">
						</ul>
						<ul class="use-data">

						</ul>
						<div id="main3" style="margin-top:5px;width:100%;height:200px;"></div>
					</div>
					<span class="left-top"></span>
					<span class="right-top"></span>
					<span class="left-bottom"></span>
					<span class="right-bottom"></span>
				</div>
			</div>


			<div class="clearfix"></div>
		</div>
		<link href="css/test-1280.css" rel="stylesheet" type="text/css">
		<script type="text/javascript" src="js/main1.js"></script>
		<script type="text/javascript" src="js/main2.js"></script>
		<script type="text/javascript" src="js/main3.js"></script>
		<script type="text/javascript" src="js/main4.js"></script>
		<script type="text/javascript" src="js/main5.js"></script>
		<script type="text/javascript" src="js/main6.js"></script>
		<script type="text/javascript" src="js/main7.js"></script>
		<script type="text/javascript" src="js/main.js"></script>
		<!-- <script type="text/javascript"> 
//根据不同的分辨率调用不同的css和js
	if(window.screen.width>=1600){
		document.write("<link href='css/test-1920.css' rel='stylesheet' type='text/css'>");  
		
		document.writeln("<script type=\"text/javascript\" src=\"js/test-1920.js\"><\/script>");
	}
	else if(window.screen.width<1600&&window.screen.width>=1280){
		document.write("<link href='css/test-1280.css' rel='stylesheet' type='text/css'>");  
		
		document.writeln("<script type=\"text/javascript\" src=\"js/test-1280.js\"><\/script>");
	}else{
	document.write("<link href='css/test-1024.css' rel='stylesheet' type='text/css'>");  
		
		document.writeln("<script type=\"text/javascript\" src=\"js/test-1024.js\"><\/script>");
	// }
</script> -->
		<!-- <script type="text/javascript"> 
	  function autoScroll(obj){  
			$(obj).find("ul").animate({  
				marginTop : "-39px"  
			},500,function(){  
				$(this).css({marginTop : "0px"}).find("li:first").appendTo(this);  
			})  
		}  
		$(function(){  
			setInterval('autoScroll(".maquee")',2000);
		}) 
</script> -->
	</body>
</html>