/**
 * 页面初始化函数 - 负责获取数据并初始化图表
 */
function index03() {
    console.log("index03"); 
    urlType(1); 
}

/**
 * 根据数据类型获取API数据并处理
 * @param {number} dataType - 数据类型参数
 */
function urlType(dataType) {
    console.log(dataType); 
    axios({
        method: 'GET',
        url: 'http://localhost:8080/gettype'
    }).then(function(res) {
        console.log('main3 API响应:', res.data); 
        if (res.data && Array.isArray(res.data)) {
            var chartData = {
                professionalData: [], 
                enterpriseData: [],   
                incubatorData: [],    
                areaName: [],         
                topTotal1: 0,         
                topTotal2: 0,         
                topTotal3: 0          
            };
            var limitedData = res.data.slice(-12); 
            limitedData.forEach(function(item) {
                var electricity = item.electricity || 0;
                var hybrid = item.hybrid || 0;
                var total = electricity + hybrid; 
                chartData.professionalData.push(electricity);
                chartData.enterpriseData.push(hybrid);
                chartData.incubatorData.push(total);
                // 将X轴标签从"Month X"改为"X月"
                chartData.areaName.push(item.sale_day + '月'); 
            });
            res.data.forEach(function(item) {
                var electricity = item.electricity || 0;
                var hybrid = item.hybrid || 0;
                var total = electricity + hybrid;
                chartData.topTotal1 += electricity;
                chartData.topTotal2 += hybrid;
                chartData.topTotal3 += total;
            });
            initChart3(chartData); 
        } else {
            console.error('main3 数据格式错误', res.data); 
        }
    }).catch(function(error) {
        console.error('main3 API请求失败:', error); 
    });
}

/**
 * 数据类型标签点击事件处理
 * 切换激活状态并更新图表数据
 */
$('.data-label').on('click', 'li', function (index) {
    $(this).addClass('active').siblings().removeClass('active'); 
    urlType(this.getAttribute("data-type")); 
});

/**
 * 初始化销量趋势图表
 * @param {object} data - 包含销量数据的对象
 */
function initChart3(data) {
    var myChart = echarts.init(document.getElementById('main3'));
    var professionalCnt = []; 
    var enterpriseCnt = [];   
    var Tpl = "";             
    var colnames = [];        
    if (data.professionalData != null) {
        for (var i = 0; i < data.professionalData.length; i++) {
            professionalCnt.push(data.professionalData[i]);
        }
    }
    if (data.enterpriseData != null) {
        for (var i = 0; i < data.enterpriseData.length; i++) {
            enterpriseCnt.push(data.enterpriseData[i]);
        }
    }
    if (data.areaName != null) {
        colnames = data.areaName;
    }

    // 定义隶书字体名称，根据实际环境调整，常见的如 "LiSu"
    var liShuFont = "LiSu"; 

    option = {
        color: ['#55aaff', '#55aa7f'], 
        backgroundColor: 'transparent', 
        tooltip: {
            trigger: 'axis', 
            axisPointer: {
                type: 'cross', 
                crossStyle: {
                    color: '#ffaaff' 
                }
            },
            formatter: function (params) {
                var res = params[0].name + '<br/>';
                for (var i = 0, l = params.length; i < l; i++) {
                    res += '<span style="color:' + params[i].color + '">●</span> ' +
                           params[i].seriesName + ': ' + Number(params[i].value).toLocaleString() + '<br>';
                }
                return res;
            },
            backgroundColor: 'rgba(255,255,255,0.95)', 
            borderColor: '#ffffff', 
            borderRadius: 12, 
            borderWidth: 2, 
            padding: [8, 12], 
            textStyle: {
                color: '#333', 
                fontSize:20, 
                fontFamily: liShuFont  // 提示框文本字体设置为隶书
            }
        },
        legend: {
            data: ['⚡ 纯电销量', '🔋 混动销量'], 
            top: '4%', 
            right: '5%', 
            textStyle: {
                fontSize: 20, 
                color: '#ffffff', 
                fontFamily: liShuFont  // 图例文本字体设置为隶书
            },
            itemWidth: 14, 
            itemHeight: 14, 
            itemGap: 15 
        },
        grid: {
            top: '10%', 
            left: '5%', 
            right: '5%', 
            bottom: '7%', 
            containLabel: true 
        },
        xAxis: [{
            type: 'category', 
            boundaryGap: false, 
            axisLabel: {
                show: true, 
                fontSize: 20, 
                color: '#ffffff', 
                interval: 0, 
                margin: 8,
                fontFamily: liShuFont  // X轴标签字体设置为隶书
            },
            axisLine: {
                show: false 
            },
            axisTick: {
                show: false 
            },
            splitLine: {
                show: true, 
                lineStyle: {
                    color: 'rgba(85, 170, 255, 0.1)', 
                    type: 'dashed' 
                }
            },
            data: colnames 
        }],
        yAxis: [{
            type: 'value', 
            axisLine: {
                show: false 
            },
            axisTick: {
                show: false 
            },
            axisLabel: {
                color: '#ffffff', 
                fontSize: 18, 
                formatter: function(value) {
                    if (value >= 1000) {
                        return (value / 1000).toFixed(1) + 'k';
                    }
                    return value;
                },
                fontFamily: liShuFont  // Y轴标签字体设置为隶书
            },
            splitLine: {
                show: true, 
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)', 
                    type: 'dashed' 
                }
            }
        }],
        series: [{
            name: '⚡ 纯电销量', 
            type: 'line', 
            stack: 'Total', 
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(255, 255, 255, 0.8)'
                }, {
                    offset: 1,
                    color: 'rgba(255, 107, 157, 0.1)'
                }])
            },
            lineStyle: {
                width: 3, 
                color: '#FF6B9D' 
            },
            itemStyle: {
                color: '#FF6B9D', 
                borderWidth: 3, 
                borderColor: '#fff' 
            },
            symbol: 'circle', 
            symbolSize: 8, 
            emphasis: {
                focus: 'series', 
                itemStyle: {
                    borderWidth: 4, 
                    shadowBlur: 10, 
                    shadowColor: 'rgba(255, 107, 157, 0.6)' 
                }
            },
            smooth: true, 
            data: professionalCnt 
        }, {
            name: '🔋 混动销量', 
            type: 'line', 
            stack: 'Total', 
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(78, 205, 196, 0.8)'
                }, {
                    offset: 1,
                    color: 'rgba(78, 205, 196, 0.1)'
                }])
            },
            lineStyle: {
                width: 3, 
                color: '#4ECDC4' 
            },
            itemStyle: {
                color: '#4ECDC4', 
                borderWidth: 3, 
                borderColor: '#fff' 
            },
            symbol: 'circle', 
            symbolSize: 8, 
            emphasis: {
                focus: 'series', 
                itemStyle: {
                    borderWidth: 4, 
                    shadowBlur: 10, 
                    shadowColor: 'rgba(78, 205, 196, 0.6)' 
                }
            },
            smooth: true, 
            data: enterpriseCnt 
        }]
    };
    myChart.setOption(option); 
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}