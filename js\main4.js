// 数据获取函数：用于从后端接口获取月度销量统计数据，驱动表格渲染
function index04() {
    console.log("index04");
    axios({
        method: 'GET',
        url: 'http://localhost:8080/getmonth'
    }).then(function(res) {
        console.log('main4 API响应:', res.data);
        if (res.data && Array.isArray(res.data)) {
            var chartData = res.data.map(function(item) {
                return {
                    name: item.name,
                    location: item.sale_day + '月',
                    workroom: Number(item.SedanSell).toLocaleString(),
                    students: Number(item.SUVSell).toLocaleString(),
                    graduate: Number(item.MPVSell).toLocaleString(),
                    recruit: Number(item.SedanSell + item.SUVSell + item.MPVSell).toLocaleString()
                };
            });
            initChart4(chartData);
        } else {
            console.error('main4 数据格式错误', res.data);
        }
    }).catch(function(error) {
        console.error('main4 API请求失败:', error);
    });
}

// 初始化图表（表格）并添加垂直跑马灯滚动效果
function initChart4(data) { 
    var Tpl = '';
    $.each(data, function(index, item) {
        Tpl += '<li class="table-row">' +
            '<div class="table-header">' + item.name + '</div>' +
            '<div>' + item.location + '</div>' +
            '<div>' + item.workroom + '</div>' +
            '<div>' + item.students + '</div>' +
            '<div>' + item.graduate + '</div>' +
            '<div>' + item.recruit + '</div>' +
            '</li> ';
    });
    
    $('#main4').html(`
        <div class="marquee-container">
            <div class="marquee-wrapper">
                <ul>${Tpl}</ul>
            </div>
        </div>
    `);

    var style = `
    <style>
        /* 缩小跑马灯容器高度，减少整体垂直空间 */
        .marquee-container {
            width: 100%;
            height: 220px; /* 原270px，缩小50px */
            overflow: hidden;
            position: relative;
        }
        
        /* 缩小字体大小，适配更小的容器，同时设置字体为隶书、颜色为白色 */
        .table-row {
            font-size: 26px; /* 原24px，缩小6px */
            display: flex;
            border-bottom: 1px solid #eee;
            padding: 3px 0; /* 内边距缩小 */
            font-family: "LiSu", serif; /* 设置字体为隶书，回退为 serif 类字体 */
            color: #fff; /* 设置字体颜色为白色 */
        }
        
        .table-header {
            font-size:26px; /* 原20px，缩小4px */
            font-weight: bold;
            font-family: "LiSu", serif; /* 设置字体为隶书，回退为 serif 类字体 */
            color: #fff; /* 设置字体颜色为白色 */
        }
        
        #main4 ul li > div {
            padding: 3px; /* 单元格内边距缩小 */
            min-width: 70px; /* 单元格最小宽度缩小 */
            font-family: "LiSu", serif; /* 设置字体为隶书，回退为 serif 类字体 */
            color: #fff; /* 设置字体颜色为白色 */
        }
        
        .marquee-wrapper {
            display: block;
            animation: verticalMarquee 15s linear infinite; /* 动画时间缩短，滚动更快 */
        }
        
        @keyframes verticalMarquee {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); } /* 滚动距离增加，适配更小高度 */
        }
    </style>`;
    $('head').append(style);
	
    
    setTimeout(function() {
        var $wrapper = $('.marquee-wrapper');
        var $clone = $wrapper.clone();
        $clone.find('ul').html($wrapper.find('ul').html());
        $clone.appendTo('.marquee-container');
        
        $('.marquee-wrapper').css({
            'animation': 'verticalMarquee 5s linear infinite'
        });
    }, 100);
}

function autoScroll(selector) {
    var $container = $(selector);
    var scrollHeight = $container.prop('scrollHeight');
    var clientHeight = $container.height();
    
    if (scrollHeight > clientHeight) {
        var currentTop = $container.scrollTop();
        var newTop = currentTop + 1;
        
        if (newTop >= scrollHeight - clientHeight) {
            newTop = 0;
        }
        
        $container.scrollTop(newTop);
    }
}