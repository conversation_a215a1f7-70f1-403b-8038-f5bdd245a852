@font-face
{
    font-family: myFirstFont;
    src: url('../font/Fette-Engschrift.ttf');
}

body{

    background: #081325 url(../images/interact.png) center no-repeat;
    background-size:cover;
}
div{
    box-sizing: border-box;
}

.clearfix{
    clear:both;
}
.container-header{
    width:100%;
    height:74px;
    text-align: center;
    box-sizing: border-box;
}
.container-header .nowTime{
    position: absolute;
    left:20px;
    top:41px;
    font-size: 0;
}
.container-header .nowTime li{
    display: inline-block;
    width:140px;
    height:40px;
    font-size: 30px;
    color:#fff;
}
.nowTime li{
    display: inline-block;
    float: left;
    /*font-weight: 800;*/
    background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.nowTime li div{
    background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 16px;
    text-align: left;
}
.nowTime li p {
    background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 16px;
}
.container-header .location{
    position: absolute;
    right:17px;
    top:46px;
}
.location i{
    font-size: 18px;
    font-weight: 800;
    color:#7e9bc6;
}
.location span{
    font-size: 20px;
    line-height: 30px;
    color:#168fcd;
}

.container-header h3{
    line-height: 74px;
    font-size: 2.5vw;
    font-weight: 800;
    color:#fff;
}

/* 边角 */
.left-top,.right-top,.left-bottom,.right-bottom{
    position:absolute;
    width:13px;
    height:13px;
}
.left-top{
    left:0;
    top:0;
    border-left:solid 2px #045291;
    border-top:solid 2px #045291;
}
.right-top{
    right:0;
    top:0;
    border-right:solid 2px #045291;
    border-top:solid 2px #045291;
}
.left-bottom{
    left:0;
    bottom:0;
    border-left:solid 2px #045291;
    border-bottom:solid 2px #045291;
}
.right-bottom{
    right:0;
    bottom:0;
    border-right:solid 2px #045291;
    border-bottom:solid 2px #045291;
}

.resource-right .com-count-title{
    font-size: 16px;
    margin-left: 10px;

}
.com-count-title{
    color:#1bb4f9;
    padding:15px 20px;
    font-size: 18px;
}
.com-screen-content{
    width:100%;
    height:auto;
}
.com-screen-content2{
    width:100%;
    height:auto;
}
.filter-type{
    font-size: 0;
}
.filter-type li:hover{
    cursor: pointer;
}
.filter-type li{
    display: inline-block;
    width:120px;
    line-height: 40px;
    font-family: myFirstFont;
    font-size: 20px;
    text-align:center ;
    color:#024f9b;
    border:solid 1px #075797;
    background:#0d2343;
}
.filter-type li.active{
    color:white;
    background:#0c182d;
    border:solid 1px #1bb9f9;
}

.container-content{
    padding:10px 20px;
	box-sizing: border-box;
}
.count-base,.count-resource,.count-share,.count-topic{
    position:relative;
    padding:15px;
    margin-top: 10px;
    box-sizing: border-box;
}
.com-count-title{
	
    color:#1bb4f9;
    font-size: 18px;
    padding:0;
}
.count-base{
	
	margin-left:10px;
	float:left;/*浮动*/
    width:32%;
    height:350px; 
    background: url('../images/left-top3.jpg') center no-repeat;
	background-size:cover;
	
}
.count-resource{
	margin-left:10px;
	float:left;
	width:32%;
    height:350px; 
    background: url('../images/left-top3.jpg') center no-repeat;
	background-size:cover;
}
.count-share{
	margin-left:10px;
	float:left;
	width:48.4%;
    height:350px; 
    background: url('../images/left-bottom2.jpg') center no-repeat;
	background-size:cover;
}

.count-topic{
	margin-left:10px;
	float:left;
	width:48.4%;
    height:350px; 
    background: url('../images/left-bottom2.jpg') center no-repeat;
	background-size:cover;
}
.data-label{
	position:absolute;
	top:20px;
	right:10px;
}
.data-label li{
	float:left;
    width: 120px;

	text-align: center;
    font-size: 18px;
    color: #828c9d;
}
.data-label li:hover,.data-label li.active{
    color: #fff;
    background: url("../images/choose-item.png") center no-repeat;
}



@charset "utf-8";
/* author lyc */

*{ margin:0px; padding:0px; font-family:'微软雅黑'; -webkit-tap-highlight-color:rgba(0,0,0,0);  }
li{ list-style:none }
img{ border:none}
a{text-decoration:none;} 


/* -------------------------摇奖排行榜-----------------------------------  */

.topRec_List dl,.maquee{ width:90%; overflow:hidden; margin:0 auto; color:#f0ece2}
.topRec_List dd{ float:left; text-align:left; border-bottom:1px solid #1B96EE; color:#1B96EE;font-size:14px;}
.topRec_List dl dd:nth-child(1){ width:50%; height:40px; line-height:40px; }
.topRec_List dl dd:nth-child(2){ width:30%; height:40px; line-height:40px; }
.topRec_List dl dd:nth-child(3){ width:20%; height:40px; line-height:40px; }

.maquee{ height:195px;}
.topRec_List ul{ width:100%; height:195px;}
.topRec_List li{ width:100%; height:38px; line-height:38px; text-align:left; font-size:12px;color:#76dbd1}
/*.topRec_List li:nth-child(2n){ background:#077cd0}*/
.topRec_List li div{ float:left;}
.topRec_List li div:nth-child(1){ width:50%;}
.topRec_List li div:nth-child(2){ width:30%;}
.topRec_List li div:nth-child(3){ width:20%;}


.use-data{
    text-align: center;
    padding: 2px 0;
    margin-top:3%;
    height: 5%;
    background: url(../images/resource-use-data.jpg) center no-repeat;
	background-size:cover;
    border: solid 1px #093552;
    border-right: 0px;
    border-left: 0;
}
.use-data li{
    width:30%;
    font-size: 0;
    display: inline-table;
    border-right:solid 1px #1f4191;
}
.use-data li:last-child{
    border-right:0;
}
.use-data .data-count{
    color:#fff;
    font-family:myFirstFont ;
    height: 1%;
    font-size: 16px;
}
.use-data .data-name{
    color:#1bb9f9;
    font-size: 16px;
}



