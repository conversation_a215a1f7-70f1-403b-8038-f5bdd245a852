function index02() {
    // 打印调试信息，标识函数执行
    console.log("index02");
    
    // 发送GET请求获取车型销售数据
    axios({
        method: 'GET',
        url: 'http://localhost:8080/getsell'
    }).then(function(res) {
        // 打印API响应数据
        console.log('main2 API响应:', res.data);
        
        // 验证响应数据是否有效
        if (res.data && Array.isArray(res.data)) {
            // getsell API返回单条数据，包含轿车、SUV、MPV销量
            var item = res.data[0]; // 取第一条数据
            
            if (item) {
                // 提取并处理API返回的销量数据
                var chartData = {
                    sedanSell: Number(item.SedanSell) || 0,     // 轿车销量，转为数字，默认为0
                    suvSell: Number(item.SUVSell) || 0,         // SUV销量，转为数字，默认为0
                    mpvSell: Number(item.MPVSell) || 0,         // MPV销量，转为数字，默认为0
                    registeringRate: 0,                         // 轿车占比（初始化）
                    passingRate: 0,                             // SUV占比（初始化）
                    employmentRate: 0                           // MPV占比（初始化）
                };

                // 计算总体销量
                var grandTotal = chartData.sedanSell + chartData.suvSell + chartData.mpvSell;
                
                // 计算各车型占比
                if (grandTotal > 0) {
                    chartData.registeringRate = chartData.sedanSell / grandTotal;
                    chartData.passingRate = chartData.suvSell / grandTotal;
                    chartData.employmentRate = chartData.mpvSell / grandTotal;
                }

                // 调用图表初始化函数，传入处理后的数据
                initChart2(chartData);
            } else {
                console.error('main2 数据为空', res.data);
            }
        } else {
            console.error('main2 数据格式错误', res.data);
        }
    }).catch(function(error) {
        // 处理API请求错误
        console.error('main2 API请求失败:', error);
    });
}

function initChart2(data) {
    // 初始化ECharts图表实例，绑定到ID为'main2'的DOM元素
    var myChart = echarts.init(document.getElementById('main2'));
    
    // 计算总销量
    var total = data.sedanSell + data.suvSell + data.mpvSell;
    
    // 计算各车型销量占比的百分比
    var sedanPercent = ((data.sedanSell / total) * 100).toFixed(1);
    var suvPercent = ((data.suvSell / total) * 100).toFixed(1);
    var mpvPercent = ((data.mpvSell / total) * 100).toFixed(1);

    // 准备圆环图数据，包含车型名称、销量值和颜色配置
    var pieData = [
        {name: '🚗 轿车', value: data.sedanSell, itemStyle: {color: '#FF6B9D'}},
        {name: '🚙 SUV', value: data.suvSell, itemStyle: {color: '#4ECDC4'}},
        {name: '🚐 MPV', value: data.mpvSell, itemStyle: {color: '#FFE66D'}}
    ];

    // 配置ECharts图表选项
    option = {
        // 设置图表整体颜色主题
        color: ['#55aaff', '#55aa7f','#ffaa7f'],
        
        // 配置鼠标悬停提示框
        tooltip: {
            trigger: 'item',  // 触发类型为数据项
            formatter: function(params) {
                // 自定义提示框内容格式
                return params.name + '<br/>' +
                       '销量: ' + Number(params.value).toLocaleString() + '<br/>' +
                       '占比: ' + params.percent + '%';
            },
            backgroundColor: 'rgba(255,255,255,0.95)',  // 提示框背景色
            borderColor: '#FF6B9D',                      // 边框颜色
            borderRadius: 12,                           // 边框圆角
            borderWidth: 2,                             // 边框宽度
            padding: [8, 12],                           // 内边距
            textStyle: {
                color: '#333',                          // 文字颜色
                fontSize: 22,                           // 文字大小
            }
        },
        
        // 配置图例（图表下方的车型标识）
        legend: {
            orient: 'horizontal',  // 水平排列
            bottom: '1%',          // 距离底部8%
            left: 'center',        // 水平居中
            itemWidth: 12,         // 图例标记宽度
            itemHeight: 12,        // 图例标记高度
            itemGap: 20,           // 图例项间距
            textStyle: {
                fontSize: 39,      // 文字大小
                color: '#a0a8b9'   // 文字颜色
            },
            formatter: function(name) {
                return name;  // 图例标签格式化函数
            }
        },
        
        // 配置图表系列（这里修改为标准饼图）
        series: [{
            name: '车型销量分布',  // 系列名称
            type: 'pie',           // 图表类型：饼图
            // 🌟 关键修改：将圆环半径改为单一半径（形成实心饼图）
            radius: '80%',         // 饼图半径（相对于容器的百分比）
            center: ['50%', '50%'], // 饼图中心位置（调整至更居中）
            avoidLabelOverlap: false, // 不避免标签重叠（通过其他方式处理）
            itemStyle: {
                borderRadius: 8,            // 扇形圆角
                borderColor: '#fff',        // 边框颜色
                borderWidth: 5,             // 边框宽度
                shadowBlur: 10,             // 阴影模糊度
                shadowColor: 'rgba(0, 0, 0, 0.3)' // 阴影颜色
            },
            label: {
                show: true,               // 显示标签
                position: 'outside',      // 标签位置：外部
                fontSize: 20,             // 标签文字大小
                color: '#fff',            // 标签文字颜色
                formatter: function(params) {
                    // 自定义标签格式：车型名称+占比百分比
                    return params.name + '\n' + params.percent + '%';
                },
                rich: {
                    // 富文本配置，用于更复杂的标签样式
                    name: {
                        fontSize: 40,
                        color: '#fff'
                    },
                    percent: {
                        fontSize: 20,
                        fontWeight: 'bold',
                        color: '#fff'
                    }
                }
            },
            labelLine: {
                show: true,               // 显示标签连接线
                length: 15,               // 第一段连接线长度
                length2: 8,               // 第二段连接线长度
                lineStyle: {
                    color: '#a0a8b9',      // 连接线颜色
                    width: 2              // 连接线宽度
                }
            },
            emphasis: {
                // 鼠标悬停时的高亮效果
                itemStyle: {
                    shadowBlur: 20,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                },
                label: {
                    fontSize: 22,
                    fontWeight: 'bold'
                }
            },
            animationType: 'scale',        // 动画类型：缩放
            animationEasing: 'elasticOut', // 动画缓动效果：弹性
            animationDelay: function () {
                // 随机延迟动画，使效果更生动
                return Math.random() * 200;
            },
            data: pieData                  // 图表数据
        }]
    };
    
    // 应用配置到图表实例
    myChart.setOption(option);
    
    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}