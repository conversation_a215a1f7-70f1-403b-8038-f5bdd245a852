function index01() {
    console.log("index01");
    axios({
        method: 'GET',
        url: 'http://localhost:8080/getsale'
    }).then(function(res) {
        console.log('main1 API响应:', res.data);
        if (res.data && Array.isArray(res.data)) {
            var chartData = res.data.map(function(item) {
                return {
                    name: item.name,
                    value: item.sales
                };
            });
            initChart1(chartData);
        } else {
            console.error('main1 数据格式错误', res.data);
        }
    }).catch(function(error) {
        console.error('main1 API请求失败:', error);
    });
}

function initChart1(data) {
    var myChart = echarts.init(document.getElementById('main1'));

    var sourceData = data.map(function(item) {
        return {
            name: item.name,
            value: item.value
        };
    });

    sourceData.sort(function(a, b) {
        return a.value - b.value;
    });

    var brandNames = sourceData.map(function(item) {
        return item.name;
    });
    var salesData = sourceData.map(function(item) {
        return item.value;
    });

    // 定义隶书字体名称，根据实际环境调整，常见的如 "LiSu"
    var liShuFont = "LiSu"; 

    option = {
        grid: {
            borderWidth: 0,
            top: '15',
            left: '100',
            right: '30',
            bottom: '35',
            textStyle: {
                color: "#fff",
                fontFamily: liShuFont  // 网格内文本（若有）设置字体
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderColor: '#ffffff',
            borderRadius: 8,
            borderWidth: 2,
            padding: [5, 10],
            formatter: function(params) {
                var res = '';
                for (var i = 0, l = params.length; i < l; i++) {
                    res += '' + params[i].seriesName + ' : ' + params[i].value + '<br>';
                }
                return res;
            },
            textStyle: {
                fontFamily: liShuFont  ,// 提示框文本字体
				fontSize:24,
            }
        },
        xAxis: {
            type: 'value',
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                fontSize: 18,
                fontWeight: 100,
                textStyle: {
                    color: '#ffffff',
                    fontFamily: liShuFont  // X轴标签字体
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255,255,255,0.1)'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: brandNames,
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                fontSize: 18,
                fontWeight: 100,
                textStyle: {
                    color: '#ffffff',
                    fontFamily: liShuFont  // Y轴标签字体
                }
            }
        },
        series: [{
            name: '品牌销量',
            type: 'bar',
            data: salesData,
            itemStyle: {
                normal: {
                    color: function(params) {
                        var colors = ['#7fffbd', '#72e5aa','#63c894','#55aa7f','#50a278','#48916b','#408260','#3e7c5c','#33674c','#204230',
                        ];
                        return colors[params.dataIndex % colors.length];
                    },
                    barBorderRadius: [0, 5, 5, 0]
                }
            },
            label: {
                show: true,
                position: 'right',
                fontSize: 19,
                color: '#fff',
                formatter: function(params) {
                    return Number(params.value).toLocaleString();
                },
                fontFamily: liShuFont  // 系列标签（柱子上的文本）字体
            },
            barWidth: '60%'
        }]
    };

    myChart.setOption(option);
}